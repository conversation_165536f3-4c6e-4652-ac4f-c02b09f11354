import {fireEvent, render, screen} from '@testing-library/react'
import VotingSection from './VotingSection'
import React from 'react'

const votingParticipants = [{id:1,name:'<PERSON>',avatar:'avatar1'}]
const votingParticipants2 = [{id:2,name:'<PERSON>',avatar:'avatar2'}]

describe('When interacting with VotingSection', () => {
  it('should call onSelect when a card is clicked', () => {
    // Arrange
    const onSelect = vi.fn()

    // Act
    render(
      <VotingSection
        selectedCard={null}
        onSelect={onSelect}
        participantsRows={[votingParticipants, votingParticipants2]}
      />
    )
    fireEvent.click(screen.getByText('0'))

    // Assert
    expect(onSelect).toHaveBeenCalled()
  })
})
