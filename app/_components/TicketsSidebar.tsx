"use client"
import React from 'react'
import {Badge, Box, Button, Card, Flex, IconButton, Text} from '@radix-ui/themes'
import {MoreHorizontal, Plus} from 'lucide-react'

export interface Ticket {
  id: string
  title: string
  status: string
  votes: number
}

export default function TicketsSidebar({tickets}: {tickets: Ticket[]}) {
  return (
    <Box height="100%" style={{overflowY: 'auto', paddingRight: '8px'}}>
      <Button variant="outline" size={{initial: '1', xl: '2'}} style={{width: '100%', marginBottom: '12px'}}>
        <Plus size={14}/>
        ADD ANOTHER
      </Button>
      <Flex direction="column" gap={{initial: '3', xl: '4'}}>
        {tickets.map((ticket) => (
          <Card key={ticket.id} size="2">
            <Flex justify="between" align="start" mb={{initial: '1', xl: '2'}}>
              <Flex align="center" gap="2" wrap="wrap">
                {ticket.status === 'Estimated' && (
                  <Badge color="green" size="1">
                    {ticket.votes} SP
                  </Badge>
                )}
                {ticket.status === 'In progress' && (
                  <Badge color="plum" size="1">NOT ESTIMATED</Badge>
                )}
              </Flex>
              <IconButton variant="ghost" size="1">
                <MoreHorizontal size={14}/>
              </IconButton>
            </Flex>
            <Text
              size={{initial: '1', xl: '2'}}
              weight="medium"
              mb={{initial: '2', xl: '3'}}
              style={{lineHeight: '1.4'}}
            >
              {ticket.title}
            </Text>
            {ticket.votes === 0 && (
              <Button size={{initial: '1', xl: '2'}} style={{width: '100%', marginTop: '8px'}}>
                START VOTING
              </Button>
            )}
            {ticket.votes > 0 && (
              <Flex justify="between" direction="column" align="center" gap="2">
                <Button variant="outline" size={{initial: '1', xl: '2'}} style={{width: '100%', marginTop: '8px'}}>
                  RESET
                </Button>
              </Flex>
            )}
          </Card>
        ))}
      </Flex>
    </Box>
  )
}
