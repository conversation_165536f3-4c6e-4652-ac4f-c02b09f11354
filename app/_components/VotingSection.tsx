"use client"
import React from 'react'
import {Ava<PERSON>, <PERSON><PERSON>, Card, Flex, Text} from '@radix-ui/themes'
import {Coffee, Hourglass, ThumbsUp} from 'lucide-react'

export interface SimpleParticipant {
  id: number
  name: string
  avatar: string
}

const fibonacciCards = [0, 1, 2, 3, 5, 8, 13, 21]

interface Props {
  participantsRows: SimpleParticipant[][]
  selectedCard: number | string | null
  onSelect: (card: number | string) => void
}

export default function VotingSection({participantsRows, selectedCard, onSelect}: Props) {
  const [row1, row2] = participantsRows
  return (
    <>
      <Card size="2">
        <Flex
          justify="center"
          gap={{initial: '3', sm: '4', md: '6', lg: '8'}}
          mb={{initial: '4', md: '6'}}
          style={{overflowX: 'auto', paddingBottom: '8px'}}
          wrap={{initial: 'wrap', md: 'nowrap'}}
        >
          {row1.map((participant) => (
            <Flex key={participant.id} direction="column" align="center" gap={{initial: '1', sm: '2'}}>
              <Card
                className="backdrop-blur"
                style={{
                  width: '44px',
                  height: '60px',
                  backgroundColor: 'var(--grass-5)',
                  borderColor: 'var(--grass-6)',
                  boxShadow: '0 2px 8px var(--grass-3)'
                }}
              >
                <Flex align="center" justify="center" height="100%">
                  <ThumbsUp size={18} color="green" className="animate-bounce"/>
                </Flex>
              </Card>
              <Avatar size={{initial: '2', sm: '3'}} src={participant.avatar} fallback={participant.name.charAt(0)}/>
              <Text
                size="1"
                color="gray"
                style={{maxWidth: '60px', overflow: 'hidden', textOverflow: 'ellipsis', textAlign: 'center'}}
              >
                {participant.name}
              </Text>
            </Flex>
          ))}
        </Flex>
        <Flex justify="center" mb={{initial: '4', md: '6'}}>
          <Button color="indigo" size={{initial: '2', md: '3'}} onClick={() => onSelect('reveal')}>
            REVEAL VOTES
          </Button>
        </Flex>
        <Flex
          justify="center"
          gap={{initial: '3', sm: '4', md: '6', lg: '8'}}
          style={{overflowX: 'auto', paddingBottom: '8px'}}
          wrap={{initial: 'wrap', md: 'nowrap'}}
        >
          {row2.map((participant, index) => (
            <Flex key={participant.id} direction="column" align="center" gap={{initial: '1', sm: '2'}}>
              <Card
                className="backdrop-blur"
                style={{
                  width: '44px',
                  height: '60px',
                  backgroundColor: index === 2 ? 'var(--gray-6)' : 'var(--grass-5)',
                  borderColor: index === 2 ? 'var(--gray-3)' : 'var(--grass-6)',
                  boxShadow: index === 2 ? 'none' : '0 2px 8px var(--grass-3)'
                }}
              >
                <Flex align="center" justify="center" height="100%">
                  {index === 2 ? (
                    <Hourglass size={18} color="gray"/>
                  ) : (
                    <ThumbsUp size={18} color="green" className="animate-bounce"/>
                  )}
                </Flex>
              </Card>
              <Avatar size={{initial: '2', sm: '3'}} src={participant.avatar} fallback={participant.name.charAt(0)}/>
              <Text
                size="1"
                color="gray"
                style={{maxWidth: '60px', overflow: 'hidden', textOverflow: 'ellipsis', textAlign: 'center'}}
              >
                {participant.name}
              </Text>
            </Flex>
          ))}
        </Flex>
      </Card>
      <Flex direction="column" align="center" mt={{initial: '6', md: '8'}}>
        <Button
          className="hover:cursor-pointer"
          variant="solid"
          size={{initial: '1', sm: '2'}}
          style={{width: '40px', height: '60px', backgroundColor: 'var(--indigo-9)', fontSize: '14px'}}
        />
        <Text align="center" weight="bold" color="gray" mb={{initial: '3', md: '4'}} size={{initial: '2', sm: '3'}}>
          Choose your card
        </Text>
        <Flex justify="center" gap={{initial: '1', sm: '2'}} wrap="wrap" style={{maxWidth: '100%', margin: '0 auto'}} p={{initial: '0', sm: '2'}}>
          {fibonacciCards.map((card) => (
            <Button
              key={card}
              className="hover:cursor-pointer"
              variant={selectedCard === card ? 'solid' : 'outline'}
              size={{initial: '1', sm: '2'}}
              style={{
                width: '40px',
                height: '60px',
                backgroundColor: selectedCard === card ? 'var(--indigo-9)' : undefined,
                fontSize: '14px'
              }}
              onClick={() => onSelect(card)}
            >
              {card}
            </Button>
          ))}
          <Button
            variant={selectedCard === 'coffee' ? 'solid' : 'outline'}
            size={{initial: '1', sm: '2'}}
            className="hover:cursor-pointer"
            style={{width: '40px', height: '60px', backgroundColor: selectedCard === 'coffee' ? 'var(--indigo-9)' : undefined}}
            onClick={() => onSelect('coffee')}
          >
            <Coffee size={18}/>
          </Button>
        </Flex>
      </Flex>
    </>
  )
}
