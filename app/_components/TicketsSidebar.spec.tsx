import {render, screen} from '@testing-library/react'
import TicketsSidebar from './TicketsSidebar'
import React from 'react'

const tickets = [
  {id: 'KB-1', title: 'First ticket', status: 'Estimated', votes: 3},
  {id: 'KB-2', title: 'Second ticket', status: 'In progress', votes: 0}
]

describe('When rendering TicketsSidebar', () => {
  it('should show ticket titles', () => {
    // Act
    render(<TicketsSidebar tickets={tickets} />)

    // Assert
    expect(screen.getByText('First ticket')).toBeInTheDocument()
    expect(screen.getByText('Second ticket')).toBeInTheDocument()
  })

  it('should display action based on votes', () => {
    // Act
    render(<TicketsSidebar tickets={tickets} />)

    // Assert
    expect(screen.getByText('RESET')).toBeInTheDocument()
    expect(screen.getByText('START VOTING')).toBeInTheDocument()
  })
})
