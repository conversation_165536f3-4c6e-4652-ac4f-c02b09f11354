import {render, screen} from '@testing-library/react'
import ParticipantsSidebar from './ParticipantsSidebar'
import React from 'react'

const participants = [
  {id: '1', name: '<PERSON>', avatar: 'avatar1.png', voted: true},
  {id: '2', name: '<PERSON>', avatar: 'avatar2.png', voted: true}
]

describe('When rendering ParticipantsSidebar', () => {
  it('should display participant names', () => {
    // Arrange
    
    // Act
    render(<ParticipantsSidebar participants={participants} />)
    
    // Assert
    expect(screen.getByText('<PERSON>')).toBeInTheDocument()
    expect(screen.getByText('<PERSON>')).toBeInTheDocument()
  })
})
