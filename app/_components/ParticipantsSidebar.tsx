"use client"
import React from 'react'
import {Avatar, Box, Flex, Text} from '@radix-ui/themes'

export interface Participant {
  id: string
  name: string
  avatar: string
  voted: boolean
}

export default function ParticipantsSidebar({participants}: {participants: Participant[]}) {
  return (
    <Box height="100%" style={{overflowY: 'auto', paddingRight: '8px'}}>
      <Box mb={{initial: '4', lg: '6'}}>
        <Flex align="center" gap="2" mb={{initial: '2', lg: '3'}}>
          <Text size={{initial: '1', lg: '2'}} color="gray" weight="bold">Waiting</Text>
        </Flex>
        <Flex
          align="center"
          gap={{initial: '2', lg: '3'}}
          p={{initial: '1', lg: '2'}}
          style={{
            borderRadius: '8px',
            backgroundColor: 'var(--gray-1)',
            border: '1px solid var(--gray-3)'
          }}
        >
          <Avatar size={{initial: '1', lg: '2'}} src="https://i.pravatar.cc/40?img=11" fallback="M"/>
          <Text size={{initial: '1', lg: '2'}} weight="bold">Matt</Text>
        </Flex>
      </Box>
      <Box>
        <Text size={{initial: '1', lg: '2'}} weight="bold" color="gray" mb={{initial: '2', lg: '3'}}>Voted</Text>
        <Flex direction="column" gap={{initial: '1', lg: '2'}}>
          {participants.map((participant) => (
            <Flex
              key={participant.id}
              align="center"
              gap={{initial: '2', lg: '3'}}
              p={{initial: '1', lg: '2'}}
              style={{
                borderRadius: '8px',
                backgroundColor: 'var(--gray-1)',
                border: '1px solid var(--gray-3)'
              }}
            >
              <Avatar size={{initial: '1', lg: '2'}} src={participant.avatar} fallback={participant.name.charAt(0)}/>
              <Text
                size={{initial: '1', lg: '2'}}
                style={{overflow: 'hidden', textOverflow: 'ellipsis', flex: 1}}
              >
                {participant.name}
              </Text>
            </Flex>
          ))}
        </Flex>
      </Box>
    </Box>
  )
}
