import {render, screen} from '@testing-library/react'
import React from 'react'
import PokerPlanningPage from './PokerPlanningPage'
import {ReduxProvider} from '@/app/_providers/ReduxProvider'

describe('When rendering PokerPlanningPage', () => {
  it('should display choose your card instruction', () => {
    // Arrange

    // Act
    render(
      <ReduxProvider>
        <PokerPlanningPage sessionId="session-1" />
      </ReduxProvider>
    )

    // Assert
    expect(screen.getByText('Choose your card')).toBeInTheDocument()
  })
})
