import type {Metada<PERSON>} from "next";
import localFont from "next/font/local";
import "@radix-ui/themes/styles.css";
import "./globals.css";
import {ReactNode} from "react";
import {ReactQueryClientProvider} from "@/app/_providers/ReactQueryClientProvider";
import {Theme} from "@radix-ui/themes";
import {ReduxProvider} from "@/app/_providers/ReduxProvider";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({children}: Readonly<{ children: ReactNode }>) {
  return (
    <html lang="en" className="light">
    <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
    <ReactQueryClientProvider>
      <ReduxProvider>
          <Theme appearance="light"
                 accentColor="indigo"
                 grayColor="slate"
                 radius="small"
                 hasBackground={true}
                 panelBackground="translucent"
          >
            {children}
          </Theme>
        </ReduxProvider>
    </ReactQueryClientProvider>
    </body>
    </html>
  );
}
