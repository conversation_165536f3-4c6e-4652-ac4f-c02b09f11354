import {render, screen} from '@testing-library/react'
import React from 'react'
import Page from './page'
import {ReduxProvider} from '@/app/_providers/ReduxProvider'

describe('When rendering PokerPlanning session page', () => {
  it('should display choose your card instruction', async () => {
    // Arrange
    
    // Act
    const element = await Page({params: Promise.resolve({sessionId: 'session-1'})})
    render(<ReduxProvider>{element}</ReduxProvider>)

    // Assert
    expect(screen.getByText('Choose your card')).toBeInTheDocument()
  })
})
