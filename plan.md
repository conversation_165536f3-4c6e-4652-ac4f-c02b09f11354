# Implementation Plan

This plan describes how to implement the Poker Planning use cases using Convex on the back end and Redux on the front end. It follows the clean architecture conventions and the TDD workflow described in `AGENTS.md`.

## Overview
- Bounded context: **PokerPlanning**
- Architecture layers for both client and server:
  - `domain` for entities and value objects
  - `application` for commands/queries and their handlers
  - `infrastructure` for implementations (e.g., Convex functions)
  - `presentation` (server) and React components/hooks (client)
- Testing uses Vitest with jest-extended. Each test is structured with `// Arrange`, `// Act`, `// Assert` sections and one action in the Act step.

## Setup
1. Run `npm install` once before starting development.
2. During each TDD iteration run `npm run lint` and `npm test`.
3. Keep commits focused with conventional messages and use English branch names.

## Domain Design
- **Entities**: `Participant`, `Ticket`, `Vote`, `PokerPlanningSession`.
- **Value Objects**: `TicketId`, `ParticipantId`, `VoteValue`.
- **Repositories**: `PokerPlanningSessionRepository` persisted with Convex.
- **Events**: domain events such as `TicketAdded` or `VoteRevealed` trigger presenters.

## Backend (Convex)
1. Create commands and handlers in `src/server/PokerPlanning/application/commands`.
2. Create queries and handlers in `src/server/PokerPlanning/application/queries`.
3. Define repository interfaces under `application` and implement them in `infrastructure` using the `Convex[Entity]Repository.ts` naming.
4. Add controllers in `presentation` to expose each command or query as Convex functions via the Ioctopus container.
5. Use the Convex CLI to run and deploy functions.

## Frontend (Redux)
1. Organise Redux slices under `src/client/PokerPlanning` with async thunks calling Convex functions.
2. Provide read-only selectors and connect them through hooks with no logic.
3. Build simple React components in `infrastructure` that render view models from presenters.
4. Mirror domain snapshots in the Redux state for straightforward rendering.

## Workflow
1. Create a branch in English for each use case (e.g., `feature/add-ticket`).
2. Follow a TDD cycle:
   - Write a failing acceptance test inside `src/server/PokerPlanning/specs` or `src/client/PokerPlanning/specs` depending on the behaviour.
   - Run `npm run lint` and `npm test`; ensure failures only come from the new test.
   - Implement the minimal code across the layers (domain, application, infrastructure/presentation).
   - Re-run `npm run lint` and `npm test`. Commit with a conventional message when both pass.
3. Use the repository and presenter patterns. Front end logic is in async thunks and selectors; hooks only glue components to these use cases.

## Implementation Checklist
- [ ] Open the Poker Planning page
- [ ] View participants
- [ ] Toggle participants sidebar
- [ ] View tickets
- [ ] Toggle tickets sidebar
- [ ] Add a ticket
- [ ] Start voting on a ticket
- [ ] Choose a card
- [ ] Reveal votes
- [ ] Reset votes

## Use Case Steps
The README lists the supported features in lines 21–30【F:README.md†L21-L30】. The following sections outline the steps to implement each one.

### 1. Open the Poker Planning page
- **Back end**: No command required. Provide an initial query (e.g., `GetSessionQuery`) via a Convex function that retrieves participants, tickets, and current voting state.
- **Front end**: Create a Redux slice to store the session. A thunk `loadSession` calls the query and populates the slice. The Next.js page dispatches `loadSession` on mount.
- **Tests**: Acceptance test ensures that calling `loadSession` renders the layout with sidebars and voting area when data is returned.

### 2. View participants
- **Back end**: Extend the session query or create `ListParticipantsQuery` returning all members and their voting status.
- **Front end**: Selector reads participants from the Redux store. Component `ParticipantsSidebar` displays them, highlighting waiting members.
- **Tests**: Use case test verifies that the sidebar shows every participant and highlights those without a vote.

### 3. Toggle participants sidebar
- **Back end**: No server change.
- **Front end**: Add local UI state in Redux or component context to open/close the sidebar. Provide actions `openParticipantsSidebar` and `closeParticipantsSidebar`.
- **Tests**: Ensure dispatching the toggle action updates the UI state and the sidebar visibility matches.

### 4. View tickets
- **Back end**: Add `ListTicketsQuery` providing ticket details including status and story points.
- **Front end**: Thunk `loadTickets` fetches tickets from Convex and stores them. `TicketsSidebar` uses a selector to render the list.
- **Tests**: Confirm that after `loadTickets` the sidebar lists every ticket with its status and points.

### 5. Toggle tickets sidebar
- **Back end**: No change.
- **Front end**: Similar to participants, add actions to control ticket sidebar visibility.
- **Tests**: Validate that UI state updates show or hide the sidebar accordingly.

### 6. Add a ticket
- **Back end**: Implement `AddTicketCommand` and its handler. The handler calls a Convex mutation to insert a placeholder ticket. Use `UuidIdentityProvider` for ticket ids.
- **Front end**: Thunk `addTicket` dispatches the command. After success, update the Redux store by reloading tickets or pushing the new ticket locally.
- **Tests**: Acceptance test dispatches `addTicket` and expects a new ticket to appear in the list with placeholder data.

### 7. Start voting on a ticket
- **Back end**: Implement `StartVotingCommand`. Handler marks the ticket as "In progress" and clears existing votes using a Convex mutation.
- **Front end**: Thunk `startVoting` triggers the command. Update store to reflect the voting session state.
- **Tests**: Verify that invoking `startVoting` sets the ticket status and resets votes in the store.

### 8. Reset votes
- **Back end**: Implement `ResetVotesCommand` to remove all votes for a ticket. Convex mutation updates the database.
- **Front end**: Thunk `resetVotes` calls the command and updates the ticket in the store.
- **Tests**: Ensure that after `resetVotes`, the ticket shows zero votes.

### 9. Choose a card
- **Back end**: Implement `SubmitVoteCommand` storing the participant's vote via a Convex mutation.
- **Front end**: The voting component dispatches `submitVote` when a card is selected. Selected cards are stored in Redux to reflect current votes.
- **Tests**: Acceptance test verifies that choosing a card updates the participant's vote in the store and disables further selection until votes are revealed.

### 10. Reveal votes
- **Back end**: Implement `RevealVotesCommand` that marks votes as revealed and returns the results.
- **Front end**: Thunk `revealVotes` sends the command and updates the store so each ticket displays the final points. UI shows all card selections.
- **Tests**: Validate that after calling `revealVotes` the voting section displays all participant votes.

## Additional Notes
- Use `convex-test` for back end unit tests where database interactions are required.
- Keep domain models simple (e.g., `Ticket`, `Participant`, `Vote` value objects). Factories help set up entities in tests.
- Rely on dependency injection via Ioctopus for repositories and providers. Use `FakeIdentityProvider` and in-memory repositories in tests.
- Avoid dispatching multiple Redux actions in one test. Set up initial state manually before each action.
- Use the `@/` alias for imports and never depend on concrete implementations within domain or application layers.
- All React components follow the humble object pattern with logic extracted to hooks and thunks.
- Coverage can be checked with `npm run test:coverage` if desired.

