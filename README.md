# Front & Back Clean Architecture NextJS template

## Content
- React 19
- NextJS 15
- Clean architecture template
- iOctopus ioc container (set up for back & front)
- radix-ui + radix-ui themes
- tanstack query + tanstack devtools
- vitest + vitest-mock-extended + vite-tsconfig-paths + @vitest/coverage-v8
- testing library
- auto-animate
- depcruise (with clean architecture rules)

Note: Edit the tools/github.js file to change the repository url.

## Use Cases

The available components let you simulate a complete Poker Planning session. The current UI supports the following use cases:

1. **Open the Poker Planning page** – display the main layout with sidebars and voting area.
2. **View participants** – show all members and highlight those still waiting to vote.
3. **Toggle participants sidebar** – open or close the participants list on mobile.
4. **View tickets** – list every ticket with its status and story points when estimated.
5. **Toggle tickets sidebar** – open or close the tickets list on mobile.
6. **Add a ticket** – create a placeholder ticket from the "ADD ANOTHER" button.
7. **Start voting on a ticket** – launch a voting session for tickets with no votes.
8. **Reset votes** – clear votes on a ticket that already has estimations.
9. **Choose a card** – select a <PERSON><PERSON><PERSON>ci or coffee card to cast your vote.
10. **Reveal votes** – show all participants' selections once everyone has voted.
