import {query} from './_generated/server';
import {v} from 'convex/values';
import { GetPokerPlanningSessionController } from '@/src/server/PokerPlanning/presentation/controllers/GetPokerPlanningSessionController';

export const getSession = query({
  args: { sessionId: v.id('sessions') },
  handler: async (ctx, args) => {
    const controller = new GetPokerPlanningSessionController();
    return controller.handle(ctx, { sessionId: args.sessionId });
  },
});
