import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  sessions: defineTable({
    participants: v.array(
      v.object({
        id: v.string(),
        name: v.string(),
        avatar: v.string(),
        voted: v.boolean(),
      }),
    ),
    tickets: v.array(
      v.object({
        id: v.string(),
        title: v.string(),
        status: v.string(),
        votes: v.number(),
      }),
    ),
  }),
});
