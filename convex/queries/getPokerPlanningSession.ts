import {query} from '../_generated/server';
import {v} from 'convex/values';
import { GetPokerPlanningSessionController } from '@/src/server/PokerPlanning/presentation/controllers/GetPokerPlanningSessionController';
import { ConvexPokerPlanningSessionRepository } from '@/src/server/PokerPlanning/infrastructure/ConvexPokerPlanningSessionRepository';
import { GetSessionQueryHandler } from '@/src/server/PokerPlanning/application/queries/getSession/GetSessionQueryHandler';

export const endpoint = query({
  args: { sessionId: v.id('sessions') },
  handler: async (ctx, args) => {
    const repository = new ConvexPokerPlanningSessionRepository(ctx);
    const handler = new GetSessionQueryHandler(repository);
    const controller = new GetPokerPlanningSessionController(handler);
    return controller.handle({ sessionId: args.sessionId });
  },
});
