import {render, screen} from '@testing-library/react';
import {act} from 'react-dom/test-utils';
import React from 'react';
import {ReduxProvider} from '@/app/_providers/ReduxProvider';
import {useAppSelector} from '@/src/client/Shared/store/appStore/hooks';
import {increment} from '@/src/client/Shared/store/counterSlice';
import {reduxStore} from '@/src/client/Shared/store/reduxStore/reduxStore';

describe('When using ReduxProvider', () => {
  it('should provide initial counter value', () => {
    // Arrange
    const CounterValue = () => {
      const value = useAppSelector(state => state.counter.value);
      return <span>{value}</span>;
    };

    // Act
    render(
      <ReduxProvider>
        <CounterValue />
      </ReduxProvider>
    );

    // Assert
    expect(screen.getByText('0')).toBeInTheDocument();
  });

  it('should update value when increment action is dispatched', () => {
    // Arrange
    const CounterValue = () => {
      const value = useAppSelector(state => state.counter.value);
      return <span>{value}</span>;
    };

    // Act
    render(
      <ReduxProvider>
        <CounterValue />
      </ReduxProvider>
    );
    act(() => {
      reduxStore.dispatch(increment());
    });

    // Assert
    expect(screen.getByText('1')).toBeInTheDocument();
  });
});
