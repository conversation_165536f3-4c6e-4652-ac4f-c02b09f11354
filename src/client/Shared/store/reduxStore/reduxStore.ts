import {configureStore} from '@reduxjs/toolkit';
import {rootReducers} from '../appStore/rootReducers';
import {rootInitialState} from '../appStore/rootInitialState';
import {PokerPlanningSessionRepository} from '@/src/client/PokerPlanning/application/ports/PokerPlanningSessionRepository';

export const createReduxStore = (dependencies: {pokerPlanningSessionRepository: PokerPlanningSessionRepository}) =>
    configureStore({
        reducer: rootReducers,
        preloadedState: rootInitialState,
        middleware: (getDefaultMiddleware) =>
            getDefaultMiddleware({
                thunk: {extraArgument: dependencies},
            }),
    });

export const reduxStore = createReduxStore({
    pokerPlanningSessionRepository: {findById: async () => ({participants: [], tickets: []})},
});

export type AppDispatch = typeof reduxStore.dispatch;
export type RootState = ReturnType<typeof reduxStore.getState>;
