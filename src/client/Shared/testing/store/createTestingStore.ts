import {configureStore} from '@reduxjs/toolkit';
import {rootReducers} from '../../store/appStore/rootReducers';
import {rootInitialState} from '../../store/appStore/rootInitialState';
import {PokerPlanningSessionRepository} from '@/src/client/PokerPlanning/application/ports/PokerPlanningSessionRepository';

export const createTestingStore = (dependencies: {pokerPlanningSessionRepository: PokerPlanningSessionRepository}) =>
    configureStore({
        reducer: rootReducers,
        preloadedState: rootInitialState,
        middleware: (getDefaultMiddleware) =>
            getDefaultMiddleware({
                thunk: {extraArgument: dependencies},
            }),
    });
