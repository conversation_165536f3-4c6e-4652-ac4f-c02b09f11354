import { describe, it, expect } from 'vitest';
import { AnyAction } from '@reduxjs/toolkit';
import { ThunkAction } from 'redux-thunk';
import { createTestingStore } from './createTestingStore';
import type { RootState } from '../../store/reduxStore/reduxStore';
import type { ThunkExtraArg } from '../../store/appStore/thunkExtra';
import { PokerPlanningSessionRepository } from '@/src/client/PokerPlanning/application/ports/PokerPlanningSessionRepository';

class CustomRepository implements PokerPlanningSessionRepository {
  async findById() {
    return { participants: [], tickets: [] };
  }
}

describe('When creating a testing store', () => {
  it('should use a default repository', () => {
    // Arrange
    const store = createTestingStore();
    let extra: unknown;

    const captureExtra: ThunkAction<void, RootState, ThunkExtraArg, AnyAction> = (
      _dispatch,
      _getState,
      arg,
    ) => {
      extra = arg;
    };

    // Act
    store.dispatch(captureExtra);

    // Assert
    expect((extra as ThunkExtraArg).pokerPlanningSessionRepository).toBeDefined();
  });

  it('should allow overriding the repository', () => {
    // Arrange
    const store = createTestingStore({}, { pokerPlanningSessionRepository: new CustomRepository() });
    let extra: unknown;

    const captureExtra: ThunkAction<void, RootState, ThunkExtraArg, AnyAction> = (
      _dispatch,
      _getState,
      arg,
    ) => {
      extra = arg;
    };

    // Act
    store.dispatch(captureExtra);

    // Assert
    expect((extra as ThunkExtraArg).pokerPlanningSessionRepository).toBeInstanceOf(CustomRepository);
  });
});
