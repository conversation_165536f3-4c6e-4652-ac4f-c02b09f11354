import {createAsyncThunk} from '@reduxjs/toolkit';
import {PokerPlanningSessionRepository} from './ports/PokerPlanningSessionRepository';
import {pokerPlanningSessionLoadingStarted, pokerPlanningSessionLoaded} from './sessionSlice';

export const loadSession = createAsyncThunk<void, string, {extra: {pokerPlanningSessionRepository: PokerPlanningSessionRepository}}>(
    'pokerPlanning/loadSession',
    async (sessionId, {dispatch, extra}) => {
        dispatch(pokerPlanningSessionLoadingStarted());
        const session = await extra.pokerPlanningSessionRepository.findById(sessionId);
        if (session) {
            dispatch(pokerPlanningSessionLoaded(session));
        }
    }
);
