import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {PokerPlanningSession} from '@/src/server/PokerPlanning/domain/PokerPlanningSession';

export interface PokerPlanningSessionState {
    participants: PokerPlanningSession['participants'];
    tickets: PokerPlanningSession['tickets'];
    isLoading: boolean;
}

const initialState: PokerPlanningSessionState = {
    participants: [],
    tickets: [],
    isLoading: false,
};

const sessionSlice = createSlice({
    name: 'pokerPlanningSession',
    initialState,
    reducers: {
        pokerPlanningSessionLoadingStarted(state) {
            state.isLoading = true;
        },
        pokerPlanningSessionLoaded(state, action: PayloadAction<PokerPlanningSession>) {
            state.participants = action.payload.participants;
            state.tickets = action.payload.tickets;
            state.isLoading = false;
        },
    },
});

export const {pokerPlanningSessionLoadingStarted, pokerPlanningSessionLoaded} = sessionSlice.actions;
export const pokerPlanningSessionReducer = sessionSlice.reducer;
export const pokerPlanningSessionInitialState = initialState;
