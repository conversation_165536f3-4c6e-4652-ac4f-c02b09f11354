import { PokerPlanningSession } from '@/src/server/PokerPlanning/domain/PokerPlanningSession';
import { createFakeParticipant } from './fakeParticipant';
import { createFakeTicket } from './fakeTicket';

export const createFakeSession = (
  overrides: Partial<PokerPlanningSession> = {},
): PokerPlanningSession => ({
  participants: overrides.participants ?? [createFakeParticipant()],
  tickets: overrides.tickets ?? [createFakeTicket()],
});

export const DEFAULT_SESSION: PokerPlanningSession = createFakeSession();
