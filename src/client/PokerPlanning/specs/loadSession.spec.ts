import {describe, it, expect} from 'vitest';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {loadSession} from '@/src/client/PokerPlanning/application/loadSession';

const createDeferred = <T>() => {
    let resolve!: (value: T) => void;
    const promise = new Promise<T>((res) => {
        resolve = res;
    });
    return {promise, resolve};
};


describe('When loading the poker planning session', () => {
    it('should store participants and tickets', async () => {
        // Arrange
        const session = {
            participants: [{id: '1', name: '<PERSON>', avatar: 'avatar', voted: false}],
            tickets: [{id: 'T1', title: 'Test', status: 'new', votes: 0}],
        };
        const store = createTestingStore({pokerPlanningSessionRepository: {findById: async () => session}});

        // Act
        await store.dispatch(loadSession('session-1'));

        // Assert
        expect(store.getState().pokerPlanningSession.participants).toHaveLength(1);
        expect(store.getState().pokerPlanningSession.tickets).toHaveLength(1);
    });

    it('should toggle loading state', async () => {
        // Arrange
        const session = {
            participants: [{id: '1', name: 'John', avatar: 'avatar', voted: false}],
            tickets: [{id: 'T1', title: 'Test', status: 'new', votes: 0}],
        };
        const deferred = createDeferred<typeof session>();
        const store = createTestingStore({pokerPlanningSessionRepository: {findById: () => deferred.promise}});

        // Act
        const dispatchPromise = store.dispatch(loadSession('session-1'));

        // Assert
        expect(store.getState().pokerPlanningSession.isLoading).toBe(true);
        deferred.resolve(session);
        await dispatchPromise;
        expect(store.getState().pokerPlanningSession.isLoading).toBe(false);
    });
});
