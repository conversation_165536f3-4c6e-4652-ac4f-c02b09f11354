import { QueryCtx } from '@/convex/_generated/server';
import { Id } from '@/convex/_generated/dataModel';
import { GetSessionQueryHandler } from '@/src/server/PokerPlanning/application/queries/getSession/GetSessionQueryHandler';
import { ConvexPokerPlanningSessionRepository } from '@/src/server/PokerPlanning/infrastructure/ConvexPokerPlanningSessionRepository';
import { GetSessionWebPresenter } from '@/src/server/PokerPlanning/presentation/presenters/GetSessionWebPresenter';

export class GetPokerPlanningSessionController {
  async handle(ctx: QueryCtx, { sessionId }: { sessionId: Id<'sessions'> }) {
    const repository = new ConvexPokerPlanningSessionRepository(ctx);
    const presenter = new GetSessionWebPresenter();
    const handler = new GetSessionQueryHandler(repository);
    await handler.execute({ sessionId }, presenter);
    return presenter.getViewModel();
  }
}
