import { PokerPlanningSession } from '@/src/server/PokerPlanning/domain/PokerPlanningSession';
import { GetSessionPresenter } from './GetSessionPresenter';
import { GetSessionViewModel } from './GetSessionViewModel';

export class GetSessionWebPresenter implements GetSessionPresenter {
  private viewModel: GetSessionViewModel = {
    session: null,
    error: undefined,
  };

  present(session: PokerPlanningSession): void {
    this.viewModel.session = session;
  }

  presentNotFound(): void {
    this.viewModel.error = 'Session not found';
  }

  getViewModel(): GetSessionViewModel {
    return this.viewModel;
  }
}
