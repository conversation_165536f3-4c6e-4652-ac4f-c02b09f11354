import {describe, it, expect} from 'vitest';
import { GetSessionQueryHandler } from '@/src/server/PokerPlanning/application/queries/getSession/GetSessionQueryHandler';
import { InMemoryPokerPlanningSessionRepository } from '@/src/server/PokerPlanning/infrastructure/InMemoryPokerPlanningSessionRepository';
import { GetSessionWebPresenter } from '@/src/server/PokerPlanning/presentation/presenters/GetSessionWebPresenter';
import { createFakeSession } from '../helpers/fakeSession';
import type { GetSessionQuery } from '@/src/server/PokerPlanning/application/queries/getSession/GetSessionQuery';

describe('When getting a poker planning session', () => {
  describe('When the session exists', () => {
    it('should return stored participants', async () => {
      // Arrange
      const storedSession = createFakeSession();
      const sessions = { 'session-1': storedSession };
      const repository = new InMemoryPokerPlanningSessionRepository(sessions);
      const handler = new GetSessionQueryHandler(repository);
      const presenter = new GetSessionWebPresenter();
      const query: GetSessionQuery = { sessionId: 'session-1' };

      // Act
      await handler.execute(query, presenter);

      // Assert
      const { session } = presenter.getViewModel();
      expect(session?.participants).toHaveLength(1);
    });

    it('should return stored tickets', async () => {
      // Arrange
      const storedSession = createFakeSession();
      const sessions = { 'session-1': storedSession };
      const repository = new InMemoryPokerPlanningSessionRepository(sessions);
      const handler = new GetSessionQueryHandler(repository);
      const presenter = new GetSessionWebPresenter();
      const query: GetSessionQuery = { sessionId: 'session-1' };

      // Act
      await handler.execute(query, presenter);

      // Assert
      const { session } = presenter.getViewModel();
      expect(session?.tickets).toHaveLength(1);
    });
  });

  describe('When the session is missing', () => {
    it('should present an error', async () => {
      // Arrange
      const repository = new InMemoryPokerPlanningSessionRepository({});
      const handler = new GetSessionQueryHandler(repository);
      const presenter = new GetSessionWebPresenter();
      const query: GetSessionQuery = { sessionId: 'unknown' };

      // Act
      await handler.execute(query, presenter);

      // Assert
      const { session, error } = presenter.getViewModel();
      expect(session).toBeNull();
      expect(error).toBe('Session not found');
    });
  });
});
