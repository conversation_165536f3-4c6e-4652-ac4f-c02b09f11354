import {describe, it, expect} from 'vitest';
import schema from '@/convex/schema';
import { ConvexPokerPlanningSessionRepository } from '@/src/server/PokerPlanning/infrastructure/ConvexPokerPlanningSessionRepository';
import { createFakeSession } from './helpers/fakes';
import {convexTest} from '@/src/server/specs/convexTest';

const session = createFakeSession();

describe('When reading from Convex repository', () => {
    it('should return stored session', async () => {
        // Arrange
        const t = convexTest(schema);
        const sessionId = await t.run(async ctx => {
            return ctx.db.insert('sessions', {participants: session.participants, tickets: session.tickets});
        });

        // Act
        const result = await t.run(async ctx => {
            const repo = new ConvexPokerPlanningSessionRepository(ctx);
            return repo.findById(sessionId);
        });

        // Assert
        expect(result.participants).toHaveLength(1);
        expect(result.tickets).toHaveLength(1);
    });

    it('should return null when session is missing', async () => {
        // Arrange
        const t = convexTest(schema);

        // Act
        const result = await t.run(async ctx => new ConvexPokerPlanningSessionRepository(ctx).findById('unknown'));

        // Assert
        expect(result).toBeNull();
    });
});
