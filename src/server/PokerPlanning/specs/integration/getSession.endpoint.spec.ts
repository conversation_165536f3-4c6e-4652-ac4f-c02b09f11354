import {describe, it, expect} from 'vitest';
import schema from '@/convex/schema';
import { api } from '@/convex/_generated/api';
import { createFakeSession } from '../helpers/fakeSession';
import {convexTest} from '@/src/server/specs/convexTest';

describe('When calling getSession endpoint', () => {
    it('should return the session data', async () => {
        // Arrange
        const t = convexTest(schema);
        const storedSession = createFakeSession();
        const sessionId = await t.run(async ctx =>
            ctx.db.insert('sessions', {
                participants: storedSession.participants,
                tickets: storedSession.tickets,
            }),
        );

        // Act
        const { session } = await t.query(api.queries.getPokerPlanningSession.endpoint, { sessionId });

        // Assert
        expect(session!.participants).toHaveLength(1);
        expect(session!.tickets).toHaveLength(1);
    });
});
