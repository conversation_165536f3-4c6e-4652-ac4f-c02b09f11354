import {describe, it, expect} from 'vitest';
import { GetSessionQueryHandler } from '@/src/server/PokerPlanning/application/queries/getSession/GetSessionQueryHandler';
import { InMemoryPokerPlanningSessionRepository } from '@/src/server/PokerPlanning/infrastructure/InMemoryPokerPlanningSessionRepository';
import { GetSessionWebPresenter } from '@/src/server/PokerPlanning/presentation/presenters/GetSessionWebPresenter';
import { createFakeSession } from './helpers/fakes';
import type { GetSessionQuery } from '@/src/server/PokerPlanning/application/queries/getSession/GetSessionQuery';

describe('When getting a poker planning session', () => {
    it('should return participants and tickets', async () => {
        // Arrange
        const storedSession = createFakeSession();
        const sessions = { 'session-1': storedSession };
        const repository = new InMemoryPokerPlanningSessionRepository(sessions);
        const handler = new GetSessionQueryHandler(repository);
        const presenter = new GetSessionWebPresenter();
        const query: GetSessionQuery = { sessionId: 'session-1' };

        // Act
        await handler.execute(query, presenter);
        const { session } = presenter.getViewModel();

        // Assert
        expect(session?.participants).toHaveLength(1);
        expect(session?.tickets).toHaveLength(1);
    });

    it('should present an error when session is missing', async () => {
        // Arrange
        const repository = new InMemoryPokerPlanningSessionRepository({});
        const handler = new GetSessionQueryHandler(repository);
        const presenter = new GetSessionWebPresenter();
        const query: GetSessionQuery = { sessionId: 'unknown' };

        // Act
        await handler.execute(query, presenter);
        const { session, error } = presenter.getViewModel();

        // Assert
        expect(session).toBeNull();
        expect(error).toBe('Session not found');
    });
});
