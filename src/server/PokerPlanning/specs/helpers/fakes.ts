import { Participant, Ticket, PokerPlanningSession } from '@/src/server/PokerPlanning/domain/PokerPlanningSession';

export const createFakeParticipant = (
  overrides: Partial<Participant> = {},
): Participant => ({
  id: overrides.id ?? '1',
  name: overrides.name ?? 'John',
  avatar: overrides.avatar ?? 'avatar',
  voted: overrides.voted ?? false,
});

export const createFakeTicket = (
  overrides: Partial<Ticket> = {},
): Ticket => ({
  id: overrides.id ?? 'T1',
  title: overrides.title ?? 'Test',
  status: overrides.status ?? 'new',
  votes: overrides.votes ?? 0,
});

export const createFakeSession = (
  overrides: Partial<PokerPlanningSession> = {},
): PokerPlanningSession => ({
  participants: overrides.participants ?? [createFakeParticipant()],
  tickets: overrides.tickets ?? [createFakeTicket()],
});

export const JOHN: Participant = createFakeParticipant();
export const TICKET_1: Ticket = createFakeTicket();
export const DEFAULT_SESSION: PokerPlanningSession = createFakeSession();
