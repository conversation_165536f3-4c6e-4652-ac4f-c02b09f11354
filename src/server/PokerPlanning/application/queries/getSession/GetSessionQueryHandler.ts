import { PokerPlanningSessionRepository } from '@/src/server/PokerPlanning/application/ports/PokerPlanningSessionRepository';
import { GetSessionPresenter } from '@/src/server/PokerPlanning/presentation/presenters/GetSessionPresenter';
import { GetSessionQuery } from './GetSessionQuery';

export class GetSessionQueryHandler {
  constructor(private readonly repository: PokerPlanningSessionRepository) {}

  async execute(
    query: GetSessionQuery,
    presenter: GetSessionPresenter,
  ): Promise<void> {
    const session = await this.repository.findById(query.sessionId);
    if (session) {
      presenter.present(session);
    } else {
      presenter.presentNotFound();
    }
  }
}
