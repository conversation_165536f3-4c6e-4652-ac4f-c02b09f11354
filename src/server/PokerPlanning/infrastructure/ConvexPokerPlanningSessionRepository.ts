import {QueryCtx, Id} from 'convex/server';
import {PokerPlanningSessionRepository} from '@/src/server/PokerPlanning/application/ports/PokerPlanningSessionRepository';
import {PokerPlanningSession} from '@/src/server/PokerPlanning/domain/PokerPlanningSession';

export class ConvexPokerPlanningSessionRepository implements PokerPlanningSessionRepository {
    constructor(private readonly ctx: QueryCtx) {}

    async findById(id: string): Promise<PokerPlanningSession | null> {
        const session = await this.ctx.db.get(id as Id<'sessions'>);
        if (!session) {
            return null;
        }
        return {
            participants: session.participants ?? [],
            tickets: session.tickets ?? [],
        };
    }
}
